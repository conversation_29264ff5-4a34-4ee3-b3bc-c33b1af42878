/* Command Log Viewer Styles */

#commandLogModal .modal-dialog {
    max-width: 90vw;
}

#command-log-content {
    background-color: #1e1e1e;
    color: #d4d4d4;
    border: 1px solid #3c3c3c;
    border-radius: 6px;
    font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.command-log-lines {
    padding: 0;
}

.log-line {
    padding: 2px 8px;
    border-left: 3px solid transparent;
    margin: 0;
    display: flex;
    align-items: flex-start;
    min-height: 20px;
}

.log-line:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

.log-line.search-highlight {
    background-color: rgba(255, 255, 0, 0.2);
    border-left-color: #ffc107;
}

.log-line .line-number {
    min-width: 40px;
    text-align: right;
    user-select: none;
    opacity: 0.6;
    font-size: 11px;
    padding-right: 8px;
    flex-shrink: 0;
}

.log-line .line-content {
    flex: 1;
    word-break: break-all;
}

/* Log level colors */
.log-line.text-danger {
    border-left-color: #dc3545;
}

.log-line.text-warning {
    border-left-color: #ffc107;
}

.log-line.text-info {
    border-left-color: #0dcaf0;
}

.log-line.text-success {
    border-left-color: #198754;
}

.log-line.text-muted {
    opacity: 0.7;
}

/* Search highlighting */
.log-line mark {
    background-color: #ffc107;
    color: #000;
    padding: 1px 2px;
    border-radius: 2px;
}

/* Search input styling */
#log-search-input {
    font-family: inherit;
    border-radius: 4px;
}

#log-search-input:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Search info alert */
.search-info {
    margin-bottom: 10px;
    font-size: 12px;
    padding: 6px 12px;
}

/* Modal header buttons */
#commandLogModal .modal-header .btn {
    padding: 4px 8px;
    font-size: 12px;
}

#commandLogModal .modal-header .dashicons {
    margin-right: 4px;
}

/* File info in footer */
#log-file-info {
    font-size: 11px;
    color: #6c757d;
}

/* Loading state */
.text-center .spinner-border {
    width: 1rem;
    height: 1rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #commandLogModal .modal-dialog {
        max-width: 95vw;
        margin: 10px;
    }
    
    #command-log-content {
        height: 400px !important;
        font-size: 12px;
    }
    
    .log-line .line-number {
        min-width: 30px;
        font-size: 10px;
    }
    
    #commandLogModal .modal-header .btn {
        padding: 2px 6px;
        font-size: 11px;
    }
    
    #commandLogModal .modal-header .btn .dashicons {
        font-size: 14px;
    }
}

/* Dark theme adjustments for WordPress admin */
body.admin-color-midnight #command-log-content {
    background-color: #191e23;
    color: #eee;
    border-color: #32373c;
}

body.admin-color-midnight .log-line:hover {
    background-color: rgba(255, 255, 255, 0.03);
}

/* Light theme for better contrast in WordPress admin */
body:not(.admin-color-midnight) #command-log-content {
    background-color: #f8f9fa;
    color: #212529;
    border-color: #dee2e6;
}

body:not(.admin-color-midnight) .log-line:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

body:not(.admin-color-midnight) .log-line .line-number {
    color: #6c757d;
}

/* Scrollbar styling */
#command-log-content::-webkit-scrollbar {
    width: 8px;
}

#command-log-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

#command-log-content::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

#command-log-content::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* Animation for modal */
#commandLogModal.fade .modal-dialog {
    transition: transform 0.3s ease-out;
    transform: translate(0, -50px);
}

#commandLogModal.show .modal-dialog {
    transform: none;
}

/* Button hover effects */
#view-command-log-btn:hover {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

#commandLogModal .modal-header .btn:hover {
    opacity: 0.8;
}

/* Focus states for accessibility */
#log-search-input:focus,
#view-command-log-btn:focus,
#commandLogModal .btn:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}
