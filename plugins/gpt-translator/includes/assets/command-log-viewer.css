/* Command Log Viewer Styles - Bootstrap 5 Compatible */

/* Log Content Container */
#command-log-content {
    background-color: #1a1d23;
    color: #e9ecef;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', '<PERSON>sol<PERSON>', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.6;
    white-space: pre-wrap;
    word-wrap: break-word;
    border: 1px solid #495057;
}

/* Light theme override */
[data-bs-theme="light"] #command-log-content,
body:not([data-bs-theme="dark"]) #command-log-content {
    background-color: #f8f9fa;
    color: #212529;
    border-color: #dee2e6;
}

/* Log Lines Styling */
.command-log-lines {
    padding: 1rem;
}

.log-line {
    padding: 0.375rem 0.75rem;
    border-left: 3px solid transparent;
    margin: 0;
    display: flex;
    align-items: flex-start;
    min-height: 1.5rem;
    transition: all 0.15s ease-in-out;
    border-radius: 0.25rem;
    margin-bottom: 0.125rem;
}

.log-line:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
    border-left-color: var(--bs-primary);
}

.log-line.search-highlight {
    background-color: rgba(var(--bs-warning-rgb), 0.2);
    border-left-color: var(--bs-warning);
    box-shadow: 0 0 0 0.125rem rgba(var(--bs-warning-rgb), 0.25);
}

.log-line .line-number {
    min-width: 3rem;
    text-align: right;
    user-select: none;
    opacity: 0.6;
    font-size: 0.75rem;
    padding-right: 0.75rem;
    flex-shrink: 0;
    font-weight: 500;
}

.log-line .line-content {
    flex: 1;
    word-break: break-word;
    font-weight: 400;
}

/* Log Level Colors - Bootstrap 5 */
.log-line.text-danger {
    border-left-color: var(--bs-danger);
    background-color: rgba(var(--bs-danger-rgb), 0.1);
}

.log-line.text-warning {
    border-left-color: var(--bs-warning);
    background-color: rgba(var(--bs-warning-rgb), 0.1);
}

.log-line.text-info {
    border-left-color: var(--bs-info);
    background-color: rgba(var(--bs-info-rgb), 0.1);
}

.log-line.text-success {
    border-left-color: var(--bs-success);
    background-color: rgba(var(--bs-success-rgb), 0.1);
}

.log-line.text-muted {
    opacity: 0.7;
    border-left-color: var(--bs-secondary);
}

.log-line.text-primary {
    border-left-color: var(--bs-primary);
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

/* Search Highlighting */
.log-line mark {
    background-color: var(--bs-warning);
    color: var(--bs-dark);
    padding: 0.125rem 0.25rem;
    border-radius: var(--bs-border-radius-sm);
    font-weight: 600;
}

/* Search Results Info */
#search-results-info {
    font-size: 0.875rem;
}

#search-results-info.show {
    display: block !important;
}

/* Modal Customizations */
#commandLogModal .modal-header {
    border-bottom: 1px solid var(--bs-border-color);
}

#commandLogModal .modal-footer {
    border-top: 1px solid var(--bs-border-color);
}

/* Button Group Styling */
#commandLogModal .btn-group .btn {
    border-radius: var(--bs-border-radius-sm);
}

/* File Info Styling */
#log-file-info {
    font-size: 0.75rem;
    color: var(--bs-secondary-color);
}

/* Loading State */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Auto-scroll Toggle */
.form-check-input:checked {
    background-color: var(--bs-success);
    border-color: var(--bs-success);
}

/* Filter Dropdown */
#log-level-filter {
    font-size: 0.875rem;
}

/* Responsive Design - Bootstrap 5 */
@media (max-width: 576px) {
    #command-log-content {
        height: 50vh !important;
        font-size: 0.75rem;
    }

    .log-line {
        padding: 0.25rem 0.5rem;
    }

    .log-line .line-number {
        min-width: 2rem;
        font-size: 0.625rem;
        padding-right: 0.5rem;
    }

    .command-log-lines {
        padding: 0.5rem;
    }
}

@media (max-width: 768px) {
    .btn-group .btn .d-none.d-md-inline {
        display: none !important;
    }
}

/* WordPress Admin Integration */
.wp-admin #commandLogModal {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    #command-log-content {
        background-color: #1a1d23;
        color: #e9ecef;
        border-color: #495057;
    }
}

/* Custom Scrollbar */
#command-log-content::-webkit-scrollbar {
    width: 0.5rem;
}

#command-log-content::-webkit-scrollbar-track {
    background: var(--bs-gray-200);
    border-radius: var(--bs-border-radius);
}

#command-log-content::-webkit-scrollbar-thumb {
    background: var(--bs-gray-400);
    border-radius: var(--bs-border-radius);
}

#command-log-content::-webkit-scrollbar-thumb:hover {
    background: var(--bs-gray-600);
}

/* Smooth Animations */
.log-line {
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-0.25rem);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Focus States - Bootstrap 5 Compatible */
#view-command-log-btn:focus-visible,
#commandLogModal .btn:focus-visible,
#log-search-input:focus {
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(var(--bs-primary-rgb), 0.25);
}

/* Empty State */
.empty-log-state {
    padding: 3rem 1rem;
    text-align: center;
    color: var(--bs-secondary-color);
}

.empty-log-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
