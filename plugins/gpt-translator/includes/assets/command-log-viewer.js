jQuery(document).ready(function($) {
    'use strict';
    
    const modal = $('#commandLogModal');
    const modalContent = $('#command-log-content');
    const searchInput = $('#log-search-input');
    const fileInfo = $('#log-file-info');
    
    // Open modal when view button is clicked
    $('#view-command-log-btn').on('click', function() {
        modal.modal('show');
        loadCommandLogContent();
    });
    
    // Refresh log content
    $('#refresh-command-log').on('click', function() {
        loadCommandLogContent();
    });
    
    // Download log file
    $('#download-command-log').on('click', function() {
        downloadCommandLog();
    });
    
    // Search functionality
    searchInput.on('input', function() {
        const searchTerm = $(this).val().toLowerCase();
        searchInLogs(searchTerm);
    });
    
    // Clear search when modal is hidden
    modal.on('hidden.bs.modal', function() {
        searchInput.val('');
        clearSearchHighlights();
    });
    
    /**
     * Load command log content via AJAX
     */
    function loadCommandLogContent() {
        modalContent.html(`
            <div class="text-center text-muted">
                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                Loading command log...
            </div>
        `);
        
        $.ajax({
            url: gptTranslatorAjax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'get_command_log_content',
                nonce: gptTranslatorAjax.commandLogNonce
            },
            success: function(response) {
                if (response.success) {
                    modalContent.html(response.data.content);
                    updateFileInfo(response.data);
                    
                    // Auto-scroll to bottom
                    setTimeout(function() {
                        modalContent.scrollTop(modalContent[0].scrollHeight);
                    }, 100);
                } else {
                    modalContent.html(`
                        <div class="alert alert-danger">
                            <strong>Error:</strong> ${response.data || 'Failed to load command log'}
                        </div>
                    `);
                }
            },
            error: function(xhr, status, error) {
                modalContent.html(`
                    <div class="alert alert-danger">
                        <strong>Error:</strong> Failed to load command log. ${error}
                    </div>
                `);
            }
        });
    }
    
    /**
     * Download command log file
     */
    function downloadCommandLog() {
        const downloadUrl = gptTranslatorAjax.ajaxUrl + '?action=download_command_log&nonce=' +
                           encodeURIComponent(gptTranslatorAjax.commandLogDownloadNonce);
        
        // Create temporary link and trigger download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = 'command.log';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    /**
     * Update file information
     */
    function updateFileInfo(data) {
        if (data.file_size && data.file_modified) {
            fileInfo.html(`
                File: command.log | 
                Size: ${data.file_size} | 
                Modified: ${data.file_modified}
            `);
        }
    }
    
    /**
     * Search within log content
     */
    function searchInLogs(searchTerm) {
        const logLines = modalContent.find('.log-line');
        let foundCount = 0;
        
        // Clear previous highlights
        clearSearchHighlights();
        
        if (!searchTerm) {
            logLines.show();
            return;
        }
        
        logLines.each(function() {
            const $line = $(this);
            const text = $line.find('.line-content').text().toLowerCase();
            
            if (text.includes(searchTerm)) {
                $line.addClass('search-highlight').show();
                foundCount++;
                
                // Highlight the search term
                const lineContent = $line.find('.line-content');
                const originalText = lineContent.text();
                const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
                const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
                lineContent.html(highlightedText);
            } else {
                $line.removeClass('search-highlight').hide();
            }
        });
        
        // Show search results count
        if (foundCount > 0) {
            // Scroll to first result
            const firstResult = modalContent.find('.search-highlight:visible').first();
            if (firstResult.length) {
                modalContent.scrollTop(
                    modalContent.scrollTop() + firstResult.position().top - modalContent.height() / 2
                );
            }
        }
        
        // Update search info
        updateSearchInfo(foundCount, searchTerm);
    }
    
    /**
     * Clear search highlights
     */
    function clearSearchHighlights() {
        const logLines = modalContent.find('.log-line');
        logLines.removeClass('search-highlight').show();
        
        // Restore original text content
        logLines.find('.line-content').each(function() {
            const $this = $(this);
            const text = $this.text(); // This removes HTML tags
            $this.text(text);
        });
        
        // Remove search info
        $('.search-info').remove();
    }
    
    /**
     * Update search information
     */
    function updateSearchInfo(count, term) {
        $('.search-info').remove();
        
        if (term) {
            const infoHtml = `
                <div class="search-info alert alert-info alert-sm py-2 mb-3">
                    ${count > 0 ? 
                        `Found ${count} result${count !== 1 ? 's' : ''} for "${term}"` : 
                        `No results found for "${term}"`
                    }
                </div>
            `;
            modalContent.prepend(infoHtml);
        }
    }
    
    /**
     * Escape special regex characters
     */
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    /**
     * Auto-refresh functionality (optional)
     */
    let autoRefreshInterval;
    
    function startAutoRefresh() {
        // Auto-refresh every 30 seconds when modal is open
        autoRefreshInterval = setInterval(function() {
            if (modal.hasClass('show')) {
                loadCommandLogContent();
            }
        }, 30000);
    }
    
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }
    
    // Start auto-refresh when modal is shown
    modal.on('shown.bs.modal', function() {
        startAutoRefresh();
    });
    
    // Stop auto-refresh when modal is hidden
    modal.on('hidden.bs.modal', function() {
        stopAutoRefresh();
    });
    
    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Ctrl/Cmd + F to focus search when modal is open
        if ((e.ctrlKey || e.metaKey) && e.key === 'f' && modal.hasClass('show')) {
            e.preventDefault();
            searchInput.focus();
        }
        
        // Escape to close modal
        if (e.key === 'Escape' && modal.hasClass('show')) {
            modal.modal('hide');
        }
    });
});
