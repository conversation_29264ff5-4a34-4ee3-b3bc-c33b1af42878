jQuery(document).ready(function($) {
    'use strict';

    // Bootstrap 5 Modal instance
    const modalElement = document.getElementById('commandLogModal');
    const modal = new bootstrap.Modal(modalElement);
    const $modal = $(modalElement);
    const modalContent = $('#command-log-content');
    const searchInput = $('#log-search-input');
    const fileInfo = $('#log-file-info');
    const searchResultsInfo = $('#search-results-info');
    const levelFilter = $('#log-level-filter');
    const autoScrollToggle = $('#auto-scroll-toggle');

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Open modal when view button is clicked
    $('#view-command-log-btn').on('click', function() {
        modal.show();
        loadCommandLogContent();
    });

    // Refresh log content
    $('#refresh-command-log').on('click', function() {
        loadCommandLogContent();
    });

    // Download log file
    $('#download-command-log').on('click', function() {
        downloadCommandLog();
    });

    // Clear search
    $('#clear-search-log').on('click', function() {
        searchInput.val('');
        levelFilter.val('');
        clearFilters();
    });

    // Search functionality
    searchInput.on('input', debounce(function() {
        applyFilters();
    }, 300));

    // Level filter
    levelFilter.on('change', function() {
        applyFilters();
    });

    // Clear search when modal is hidden
    $modal.on('hidden.bs.modal', function() {
        searchInput.val('');
        levelFilter.val('');
        clearFilters();
        stopAutoRefresh();
    });
    
    /**
     * Load command log content via AJAX
     */
    function loadCommandLogContent() {
        showLoadingState();

        $.ajax({
            url: gptTranslatorAjax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'get_command_log_content',
                nonce: gptTranslatorAjax.commandLogNonce
            },
            success: function(response) {
                if (response.success) {
                    modalContent.html(response.data.content);
                    updateFileInfo(response.data);
                    applyFilters(); // Apply any active filters

                    // Auto-scroll to bottom if enabled
                    if (autoScrollToggle.is(':checked')) {
                        setTimeout(function() {
                            modalContent.scrollTop(modalContent[0].scrollHeight);
                        }, 100);
                    }
                } else {
                    showErrorState(response.data || 'Failed to load command log');
                }
            },
            error: function(xhr, status, error) {
                showErrorState(`Failed to load command log. ${error}`);
            }
        });
    }

    /**
     * Show loading state
     */
    function showLoadingState() {
        modalContent.html(`
            <div class="d-flex justify-content-center align-items-center h-100">
                <div class="text-center text-muted">
                    <div class="spinner-border mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div>Loading command log...</div>
                </div>
            </div>
        `);
    }

    /**
     * Show error state
     */
    function showErrorState(message) {
        modalContent.html(`
            <div class="d-flex justify-content-center align-items-center h-100">
                <div class="alert alert-danger m-4" role="alert">
                    <i class="bi bi-exclamation-triangle-fill me-2"></i>
                    <strong>Error:</strong> ${message}
                </div>
            </div>
        `);
    }
    
    /**
     * Download command log file
     */
    function downloadCommandLog() {
        const downloadUrl = gptTranslatorAjax.ajaxUrl + '?action=download_command_log&nonce=' +
                           encodeURIComponent(gptTranslatorAjax.commandLogDownloadNonce);
        
        // Create temporary link and trigger download
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = 'command.log';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
    
    /**
     * Update file information
     */
    function updateFileInfo(data) {
        if (data.file_size && data.file_modified) {
            fileInfo.html(`
                File: command.log | 
                Size: ${data.file_size} | 
                Modified: ${data.file_modified}
            `);
        }
    }
    
    /**
     * Apply filters (search and level)
     */
    function applyFilters() {
        const searchTerm = searchInput.val().toLowerCase().trim();
        const levelFilter = $('#log-level-filter').val();
        const logLines = modalContent.find('.log-line');
        let visibleCount = 0;
        let totalCount = logLines.length;

        // Clear previous highlights
        clearSearchHighlights();

        logLines.each(function() {
            const $line = $(this);
            const lineText = $line.find('.line-content').text().toLowerCase();
            const lineClasses = $line.attr('class');

            let showLine = true;

            // Apply search filter
            if (searchTerm && !lineText.includes(searchTerm)) {
                showLine = false;
            }

            // Apply level filter
            if (levelFilter && !lineClasses.includes(levelFilter.toLowerCase())) {
                showLine = false;
            }

            if (showLine) {
                $line.show();
                visibleCount++;

                // Highlight search term if present
                if (searchTerm && lineText.includes(searchTerm)) {
                    $line.addClass('search-highlight');
                    highlightSearchTerm($line.find('.line-content'), searchTerm);
                }
            } else {
                $line.hide();
            }
        });

        // Update search results info
        updateSearchInfo(visibleCount, totalCount, searchTerm, levelFilter);

        // Scroll to first visible result if searching
        if (searchTerm && visibleCount > 0) {
            const firstResult = modalContent.find('.log-line:visible').first();
            if (firstResult.length) {
                modalContent.scrollTop(
                    modalContent.scrollTop() + firstResult.position().top - modalContent.height() / 3
                );
            }
        }
    }

    /**
     * Highlight search term in content
     */
    function highlightSearchTerm($element, searchTerm) {
        const originalText = $element.text();
        const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
        const highlightedText = originalText.replace(regex, '<mark>$1</mark>');
        $element.html(highlightedText);
    }

    /**
     * Clear all filters and highlights
     */
    function clearFilters() {
        clearSearchHighlights();
        modalContent.find('.log-line').show();
        updateSearchInfo(0, 0, '', '');
    }

    /**
     * Clear search highlights
     */
    function clearSearchHighlights() {
        const logLines = modalContent.find('.log-line');
        logLines.removeClass('search-highlight');

        // Restore original text content
        logLines.find('.line-content').each(function() {
            const $this = $(this);
            const text = $this.text(); // This removes HTML tags
            $this.text(text);
        });
    }
    
    /**
     * Update search information
     */
    function updateSearchInfo(visibleCount, totalCount, searchTerm, levelFilter) {
        let infoText = '';

        if (searchTerm || levelFilter) {
            const filters = [];
            if (searchTerm) filters.push(`"${searchTerm}"`);
            if (levelFilter) filters.push(`level: ${levelFilter}`);

            infoText = `Showing ${visibleCount} of ${totalCount} lines`;
            if (filters.length > 0) {
                infoText += ` (filtered by ${filters.join(', ')})`;
            }
        } else if (totalCount > 0) {
            infoText = `Showing all ${totalCount} lines`;
        }

        searchResultsInfo.text(infoText);

        if (infoText) {
            searchResultsInfo.removeClass('d-none').addClass('show');
        } else {
            searchResultsInfo.addClass('d-none').removeClass('show');
        }
    }
    
    /**
     * Escape special regex characters
     */
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }
    
    /**
     * Debounce function for search input
     */
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Auto-refresh functionality
     */
    let autoRefreshInterval;

    function startAutoRefresh() {
        // Auto-refresh every 30 seconds when modal is open
        autoRefreshInterval = setInterval(function() {
            if ($modal.hasClass('show')) {
                loadCommandLogContent();
            }
        }, 30000);
    }

    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    }

    // Start auto-refresh when modal is shown
    $modal.on('shown.bs.modal', function() {
        startAutoRefresh();
    });

    // Keyboard shortcuts
    $(document).on('keydown', function(e) {
        // Only handle shortcuts when modal is visible
        if (!$modal.hasClass('show')) return;

        // Ctrl/Cmd + F to focus search
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            searchInput.focus();
        }

        // Ctrl/Cmd + R to refresh
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            loadCommandLogContent();
        }

        // Escape to clear search or close modal
        if (e.key === 'Escape') {
            if (searchInput.val() || levelFilter.val()) {
                searchInput.val('');
                levelFilter.val('');
                clearFilters();
            } else {
                modal.hide();
            }
        }
    });

    // Auto-scroll behavior
    modalContent.on('scroll', function() {
        const isAtBottom = this.scrollTop + this.clientHeight >= this.scrollHeight - 10;
        autoScrollToggle.prop('checked', isAtBottom);
    });
});
